"use client"

import { useState, useMemo, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, ArrowUpDown, Eye, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"
import { useRouter } from "next/navigation"
import { useIsMobile } from "@/hooks/use-mobile"

interface DataTableProps {
  data: any[]
  sheetName: string
  sheetKey: string
}

// Global pagination state to maintain independent tab pagination
const tabPaginationState: Record<string, number> = {}

export function DataTable({ data, sheetName, sheetKey }: DataTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("")
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: "asc" | "desc"
  } | null>(null)
  const [currentPage, setCurrentPage] = useState(() => {
    // Initialize from global state or default to 1
    return tabPaginationState[sheetKey] || 1
  })
  const router = useRouter()
  const isMobile = useIsMobile()
  const ROWS_PER_PAGE = 25

  // Debounce search term to improve performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
      setCurrentPage(1) // Reset to first page when search changes
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Update global pagination state when current page changes
  useEffect(() => {
    tabPaginationState[sheetKey] = currentPage
  }, [currentPage, sheetKey])

  // Reset to first page when data changes
  useEffect(() => {
    setCurrentPage(1)
    tabPaginationState[sheetKey] = 1
  }, [data, sheetKey])

  const columns = useMemo(() => {
    if (!data || data.length === 0) return []
    const allColumns = Object.keys(data[0]).filter((key) => key && key.trim() !== "")

    // Reorder columns: # first, then Company Name before Company Code, then others except year columns, then year columns at the end
    const hashColumn = allColumns.find(col => col === "#")
    const companyNameColumn = allColumns.find(col => col === "Company Name" || col === "Company")
    const companyCodeColumn = allColumns.find(col => col === "Company code" || col === "Symbol")
    const yearColumns = allColumns.filter(col => ["2022", "2023", "2024"].includes(col))
    const otherColumns = allColumns.filter(col => 
      col !== "#" && 
      col !== "Company Name" && 
      col !== "Company" && 
      col !== "Company code" && 
      col !== "Symbol" && 
      !["2022", "2023", "2024"].includes(col)
    )

    const orderedColumns = []
    if (hashColumn) orderedColumns.push(hashColumn)
    if (companyNameColumn) orderedColumns.push(companyNameColumn)
    if (companyCodeColumn) orderedColumns.push(companyCodeColumn)
    orderedColumns.push(...otherColumns)
    orderedColumns.push(...yearColumns)

    // On mobile, limit to first 4 columns for better readability
    if (isMobile && orderedColumns.length > 4) {
      return orderedColumns.slice(0, 4)
    }

    return orderedColumns
  }, [data, isMobile])

  /**
   * Check if a row is empty or should be filtered out
   * For database sheet: row is empty if Company and Company code are both empty
   * For other sheets: row is empty if all values are null/undefined/empty
   */
  const isRowEmpty = (row: any) => {
    if (sheetKey === "database") {
      // For database sheet, check if key identifying columns are empty
      const company = row["Company"]
      const companyCode = row["Company code"]
      const rowNumber = row["#"]
      
      // Row is empty if both Company and Company code are empty/null
      const companyEmpty = company === null || company === undefined || String(company).trim() === "" || String(company).trim() === "-"
      const codeEmpty = companyCode === null || companyCode === undefined || String(companyCode).trim() === "" || String(companyCode).trim() === "-"
      const numberEmpty = rowNumber === null || rowNumber === undefined || String(rowNumber).trim() === "" || String(rowNumber).trim() === "-"
      
      return companyEmpty && codeEmpty && numberEmpty
    } else {
      // For other sheets, check if all values are empty
      return Object.values(row).every((value) => {
        if (value === null || value === undefined) return true
        const stringValue = String(value).trim()
        return stringValue === "" || stringValue === "-"
      })
    }
  }

  const { filteredAndSortedData, totalPages, paginatedData } = useMemo(() => {
    if (!data || data.length === 0) {
      return { filteredAndSortedData: [], totalPages: 0, paginatedData: [] }
    }

    let filtered = data

    // Filter out empty rows first
    filtered = filtered.filter((row) => !isRowEmpty(row))

    // Apply search filter using debounced search term
    if (debouncedSearchTerm) {
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) => String(value).toLowerCase().includes(debouncedSearchTerm.toLowerCase())),
      )
    }

    // Apply sorting
    if (sortConfig) {
      filtered = [...filtered].sort((a, b) => {
        const aVal = a[sortConfig.key]
        const bVal = b[sortConfig.key]

        // Handle numeric values
        const aNum = Number.parseFloat(String(aVal).replace(/[^\d.-]/g, ""))
        const bNum = Number.parseFloat(String(bVal).replace(/[^\d.-]/g, ""))

        if (!isNaN(aNum) && !isNaN(bNum)) {
          return sortConfig.direction === "asc" ? aNum - bNum : bNum - aNum
        }

        // Handle string values
        const aStr = String(aVal).toLowerCase()
        const bStr = String(bVal).toLowerCase()

        if (sortConfig.direction === "asc") {
          return aStr < bStr ? -1 : aStr > bStr ? 1 : 0
        } else {
          return aStr > bStr ? -1 : aStr < bStr ? 1 : 0
        }
      })
    }

    // Calculate pagination
    const totalPages = Math.ceil(filtered.length / ROWS_PER_PAGE)
    const startIndex = (currentPage - 1) * ROWS_PER_PAGE
    const endIndex = startIndex + ROWS_PER_PAGE
    const paginatedData = filtered.slice(startIndex, endIndex)

    return { filteredAndSortedData: filtered, totalPages, paginatedData }
  }, [data, debouncedSearchTerm, sortConfig, currentPage, ROWS_PER_PAGE])

  const handleSort = (key: string) => {
    setSortConfig((current) => ({
      key,
      direction: current?.key === key && current.direction === "asc" ? "desc" : "asc",
    }))
    // Reset to first page when sorting changes
    setCurrentPage(1)
  }

  /**
   * Handle page navigation
   */
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  /**
   * Generate page numbers for pagination controls
   */
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const startPage = Math.max(1, currentPage - 2)
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }
      
      if (endPage < totalPages) {
        pages.push('...')
        pages.push(totalPages)
      }
    }
    
    return pages
  }

  const handleRowClick = (row: any) => {
    let companyCode: string | null = null
    
    if (sheetKey === "database") {
      companyCode = row["Company code"] || row["Symbol"]
    } else if (sheetKey === "dailyPrice" || sheetKey === "marketCap") {
      companyCode = row["Symbol"]
    }
    
    if (companyCode) {
      router.push(`/company/${encodeURIComponent(companyCode)}`)
    }
  }

  const formatCellValue = (value: any) => {
    if (value === null || value === undefined || value === "") return "-"
    if (typeof value === "number") {
      return value.toLocaleString()
    }
    return String(value)
  }

  // Handle no data state
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{sheetName}</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg mb-2">
              No data available. Please upload a file to get started.
            </p>
            <p className="text-sm text-muted-foreground">
              Upload an Excel file to view and analyze your data.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-3">
          <div>
            <CardTitle className="text-lg sm:text-xl">{sheetName}</CardTitle>
            <CardDescription className="text-sm">
              Showing {paginatedData.length} of {filteredAndSortedData.length} rows
              {searchTerm && ` (filtered by "${searchTerm}")`}
              {totalPages > 0 && ` • Page ${currentPage} of ${totalPages}`}
              {isMobile && columns.length < Object.keys(data[0] || {}).length && (
                <span className="block mt-1 text-xs text-muted-foreground">
                  Tap any row to view complete details
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <div className="relative flex-1 sm:flex-none">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search data..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1) // Reset to first page when searching
                }}
                className="pl-10 w-full sm:w-64"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                {columns.map((column) => (
                  <th
                    key={column}
                    className={`text-left font-medium cursor-pointer hover:bg-muted/50 ${
                      isMobile ? "p-2 text-xs" : "p-3"
                    }`}
                    onClick={() => handleSort(column)}
                  >
                    <div className="flex items-center gap-1 sm:gap-2">
                      <span 
                        className={`truncate ${
                          isMobile ? "max-w-[100px]" : "max-w-[150px]"
                        }`} 
                        title={column}
                      >
                        {column}
                      </span>
                      <ArrowUpDown className={`text-muted-foreground ${
                        isMobile ? "h-3 w-3" : "h-4 w-4"
                      }`} />
                    </div>
                  </th>
                ))}
                {/* Always show Actions column on mobile for database sheet, or when not mobile */}
                {sheetKey === "database" && (
                  <th className={`text-left font-medium ${
                    isMobile ? "p-2 text-xs" : "p-3"
                  }`}>
                    <span className={isMobile ? "sr-only" : ""}>
                      {isMobile ? "" : "Actions"}
                    </span>
                    {isMobile && <Eye className="h-4 w-4 text-muted-foreground" />}
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {paginatedData.map((row, index) => (
                <tr
                  key={index}
                  className={`border-b hover:bg-muted/50 transition-colors ${
                    sheetKey === "database" ? "cursor-pointer" : ""
                  } ${isMobile ? "active:bg-muted" : ""}`}
                  onClick={() => handleRowClick(row)}
                >
                  {columns.map((column) => (
                    <td 
                      key={column} 
                      className={`max-w-[200px] ${
                        isMobile ? "p-2" : "p-3"
                      }`}
                    >
                      <div 
                        className={`truncate ${
                          isMobile ? "text-xs" : ""
                        }`} 
                        title={formatCellValue(row[column])}
                      >
                        {formatCellValue(row[column])}
                      </div>
                    </td>
                  ))}
                  {sheetKey === "database" && (
                    <td className={isMobile ? "p-2" : "p-3"}>
                      <Button
                        size={isMobile ? "sm" : "sm"}
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRowClick(row)
                        }}
                        className={`${
                          isMobile ? "h-8 w-8 p-0" : ""
                        }`}
                      >
                        <Eye className={`${
                          isMobile ? "h-3 w-3" : "h-4 w-4"
                        }`} />
                        {!isMobile && <span className="sr-only">View details</span>}
                      </Button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className={`flex items-center justify-between mt-4 ${
            isMobile ? "flex-col gap-3" : ""
          }`}>
            <div className={`text-sm text-muted-foreground ${
              isMobile ? "order-2" : ""
            }`}>
              Showing {((currentPage - 1) * ROWS_PER_PAGE) + 1} to {Math.min(currentPage * ROWS_PER_PAGE, filteredAndSortedData.length)} of {filteredAndSortedData.length} entries
            </div>
            
            <div className={`flex items-center gap-2 ${
              isMobile ? "order-1 overflow-x-auto pb-2" : ""
            }`}>
              {/* Previous Button */}
              <Button
                variant="outline"
                size={isMobile ? "sm" : "sm"}
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center gap-1 ${
                  isMobile ? "px-2" : ""
                }`}
              >
                <ChevronLeft className={`${
                  isMobile ? "h-3 w-3" : "h-4 w-4"
                }`} />
                {!isMobile && "Previous"}
              </Button>
              
              {/* Page Numbers */}
              <div className="flex items-center gap-1">
                {getPageNumbers().map((page, index) => (
                  <Button
                    key={index}
                    variant={page === currentPage ? "default" : "outline"}
                    size={isMobile ? "sm" : "sm"}
                    onClick={() => typeof page === 'number' && handlePageChange(page)}
                    disabled={page === '...'}
                    className={`${
                      isMobile ? "min-w-[32px] px-2" : "min-w-[40px]"
                    } ${page === '...' ? 'cursor-default' : ''}`}
                  >
                    {page}
                  </Button>
                ))}
              </div>
              
              {/* Next Button */}
              <Button
                variant="outline"
                size={isMobile ? "sm" : "sm"}
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex items-center gap-1 ${
                  isMobile ? "px-2" : ""
                }`}
              >
                {!isMobile && "Next"}
                <ChevronRight className={`${
                  isMobile ? "h-3 w-3" : "h-4 w-4"
                }`} />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

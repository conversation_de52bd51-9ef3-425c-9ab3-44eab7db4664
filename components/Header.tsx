"use client"

import { ThemeToggle } from "@/components/ThemeToggle"

/**
 * Header component that displays at the top of the application
 * Includes the theme toggle button
 */
export function Header() {
  return (
    <header className="w-full border-b border-border/50 bg-gradient-to-r from-card via-blue-50/30 to-green-50/30 dark:from-card dark:via-blue-950/30 dark:to-green-950/30 backdrop-blur-sm">
      <div className="w-full py-3 px-2 sm:px-4 flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center animate-pulse-slow shadow-colored">
            <span className="text-white font-bold text-sm">📈</span>
          </div>
          <span className="font-bold text-lg sm:text-xl bg-gradient-to-r from-primary via-accent to-info bg-clip-text text-transparent dark:from-primary dark:via-accent dark:to-info">
            Stock Market Dashboard
          </span>
        </div>
        <div className="flex items-center gap-3">
          <div className="hidden sm:flex items-center gap-2 px-3 py-1 rounded-full bg-success/10 dark:bg-success/20 text-success dark:text-success text-sm font-medium border border-success/20 dark:border-success/30">
            <div className="w-2 h-2 rounded-full bg-success animate-pulse"></div>
            Live Data
          </div>
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}
"use client"
import { FileUpload } from "@/components/FileUpload"
import { Dashboard } from "@/components/Dashboard"
import { Head<PERSON> } from "@/components/Header"
import { SavedDataList } from "@/components/SavedDataList"
import { useData } from "@/contexts/DataContext"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  const { data, isLoading, fileDate } = useData()

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/30 to-green-50/30 dark:via-blue-950/20 dark:to-green-950/20">
      <Header />
      <div className="w-full px-4 py-6">
        <div className="mb-6 animate-fade-in">
          <h1 className="text-4xl font-bold text-center mb-2 bg-gradient-to-r from-primary via-accent to-info bg-clip-text text-transparent dark:from-primary dark:via-accent dark:to-info">
            Stock Market Dashboard
            {fileDate && (
              <span className="text-lg font-normal text-muted-foreground ml-4 animate-bounce-subtle dark:text-muted-foreground">
                📊 Date: {fileDate}
              </span>
            )}
          </h1>
          <p className="text-muted-foreground text-center text-lg animate-slide-up dark:text-muted-foreground">
            Upload your Excel file to explore data across Database, Daily Price Updates, and Market Cap sheets
          </p>
        </div>

        {!data ? (
          <div className="space-y-6 animate-scale-in">
            <Card className="max-w-2xl mx-auto card-hover border-0 shadow-colored bg-gradient-to-br from-card via-blue-50/20 to-green-50/20 dark:via-blue-950/30 dark:to-green-950/30 dark:shadow-dark-elevated">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent dark:from-primary dark:to-accent flex items-center justify-center gap-2">
                  📁 Upload Excel File
                </CardTitle>
                <CardDescription className="text-base dark:text-muted-foreground">
                  Select an .xlsx file containing the required sheets: Data base, Daily price Update, and Daily Market Cap
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload />
              </CardContent>
            </Card>

            <div className="animate-slide-up">
              <SavedDataList />
            </div>
          </div>
        ) : (
          <div className="animate-fade-in">
            <Dashboard />
          </div>
        )}
      </div>
    </div>
  )
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 20% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 94%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 94%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 88%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;
    --radius: 0.75rem;

    /* Enhanced color palette for light mode */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(142 76% 36%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(199 89% 48%) 0%, hsl(217 91% 60%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(38 92% 50%) 0%, hsl(142 76% 36%) 100%);
    --shadow-colored: 217 91% 60% / 0.1;
    --shadow-accent: 142 76% 36% / 0.1;
  }

  .dark {
    --background: 222.2 84% 3%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 5%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 70%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 25%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 22%;
    --muted-foreground: 215 20.2% 80%;
    --accent: 142 76% 50%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 70%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 28%;
    --input: 217.2 32.6% 22%;
    --ring: 217.2 91.2% 70%;

    /* Enhanced dark mode colors for better visibility */
    --success: 142 76% 60%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 70%;
    --warning-foreground: 222.2 84% 4.9%;
    --info: 199 89% 70%;
    --info-foreground: 210 40% 98%;
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 70%) 0%, hsl(142 76% 60%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(199 89% 70%) 0%, hsl(217 91% 70%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(38 92% 70%) 0%, hsl(142 76% 60%) 100%);
    --shadow-colored: 217 91% 70% / 0.25;
    --shadow-accent: 142 76% 60% / 0.25;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Optimize spacing and layout */
  .container {
    @apply w-full mx-auto px-4;
  }
}

@layer components {
  .card-gradient {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(220 20% 99%) 100%);
    border: 1px solid hsl(var(--border));
    box-shadow: 0 4px 6px -1px hsl(var(--shadow-colored)), 0 2px 4px -1px hsl(var(--shadow-colored));
  }

  .dark .card-gradient {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(222.2 84% 7%) 100%);
    border: 1px solid hsl(var(--border));
    box-shadow: 0 4px 6px -1px hsl(var(--shadow-colored)), 0 2px 4px -1px hsl(var(--shadow-colored));
  }

  .card-hover {
    @apply transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1;
    box-shadow: 0 4px 6px -1px hsl(var(--shadow-colored)), 0 2px 4px -1px hsl(var(--shadow-colored));
  }

  .card-hover:hover {
    box-shadow: 0 20px 25px -5px hsl(var(--shadow-colored)), 0 10px 10px -5px hsl(var(--shadow-colored));
  }

  .dark .card-hover:hover {
    box-shadow: 0 20px 25px -5px hsl(var(--shadow-colored)), 0 10px 10px -5px hsl(var(--shadow-colored));
  }

  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }

  /* Dark mode text visibility improvements */
  .dark .text-muted-foreground {
    color: hsl(215 20.2% 80%);
  }

  /* Enhanced dark mode card styling */
  .dark .bg-card {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }

  /* Better dark mode hover states */
  .dark .hover\:bg-muted\/50:hover {
    background-color: hsl(var(--muted) / 0.6);
  }

  /* Improved dark mode table styling */
  .dark table tbody tr:hover {
    background-color: hsl(var(--muted) / 0.4);
  }

  /* Enhanced dark mode button styling */
  .dark .bg-gradient-to-r {
    background-image: var(--gradient-primary);
  }

  .dark .bg-gradient-to-br {
    background-image: var(--gradient-secondary);
  }

  /* Better dark mode border visibility */
  .dark .border-border\/50 {
    border-color: hsl(var(--border) / 0.6);
  }

  /* Improved dark mode shadow */
  .dark .shadow-colored {
    box-shadow: 0 4px 6px -1px hsl(var(--shadow-colored)), 0 2px 4px -1px hsl(var(--shadow-colored));
  }

  /* Enhanced visibility for dark mode tables and data */
  .dark table {
    border-color: hsl(var(--border));
  }

  .dark th {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
  }

  .dark td {
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
  }

  /* Better visibility for form elements in dark mode */
  .dark input, .dark textarea, .dark select {
    background-color: hsl(var(--input));
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
  }

  .dark input:focus, .dark textarea:focus, .dark select:focus {
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.prose {
  max-width: none;
}

.prose pre {
  background-color: hsl(var(--muted));
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

/* Hide scrollbars for mobile tabs */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

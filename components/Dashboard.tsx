"use client"

import { useState, useMem<PERSON> } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { RefreshCw } from "lucide-react"
import { useData } from "@/contexts/DataContext"
import { DataTable } from "@/components/DataTable"

import { SHEET_CONFIGS } from "@/utils/constants"

export function Dashboard() {
  const { data, setData } = useData()
  const [activeTab, setActiveTab] = useState("database")

  const availableSheets = useMemo(() => {
    if (!data) return []
    // Always include pricing trends if we have database data
    const sheets = SHEET_CONFIGS.filter((config) => {
      if (config.key === "pricingTrends") {
        return data.database && data.database.length > 0
      }
      return data[config.key] && data[config.key].length > 0
    })
    return sheets
  }, [data])

  const handleRefresh = () => {
    setData(null)
  }

  if (!data || availableSheets.length === 0) {
    return (
      <div className="text-center py-12 animate-fade-in">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-warning/20 to-warning/10 flex items-center justify-center">
          <span className="text-2xl">📊</span>
        </div>
        <p className="text-muted-foreground text-lg mb-4">No valid data found in the uploaded file.</p>
        <Button onClick={handleRefresh} className="gradient-primary text-white border-0 shadow-colored hover:shadow-colored-lg transition-all duration-300">
          📁 Upload New File
        </Button>
      </div>
    )
  }

  return (
    <div className="w-full space-y-6 animate-fade-in">
      <div className="w-full flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-card via-blue-50/20 to-green-50/20 dark:from-card dark:via-blue-950/30 dark:to-green-950/30 border border-border/50 shadow-colored dark:shadow-dark-elevated">
        <div className="animate-slide-up">
          <h2 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent dark:from-primary dark:to-accent flex items-center gap-2">
            📊 Data Dashboard
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground dark:text-muted-foreground mt-2">
            Explore your Excel data across <span className="font-semibold text-primary dark:text-primary">{availableSheets.length}</span> sheets. Click on any company row to view detailed analysis.
          </p>
          <div className="flex items-center gap-2 mt-3">
            <div className="flex -space-x-1">
              {availableSheets.slice(0, 3).map((sheet, index) => (
                <div key={sheet.key} className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                  index === 0 ? 'bg-primary' : index === 1 ? 'bg-accent' : 'bg-info'
                } border-2 border-background dark:border-background shadow-sm`}>
                  {index + 1}
                </div>
              ))}
            </div>
            <span className="text-sm text-muted-foreground dark:text-muted-foreground">Active data sheets</span>
          </div>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm" className="w-full sm:w-auto card-hover border-primary/20 dark:border-primary/30 hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-foreground dark:bg-card">
          <RefreshCw className="h-4 w-4 mr-2" />
          Upload New File
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full animate-scale-in">
        <TabsList className="w-full bg-gradient-to-r from-muted via-blue-50/50 to-green-50/50 dark:from-muted dark:via-blue-950/50 dark:to-green-950/50 p-1 rounded-xl shadow-colored dark:shadow-dark-elevated">
          {availableSheets.map((sheet, index) => (
            <TabsTrigger
              key={sheet.key}
              value={sheet.key}
              className="text-xs sm:text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-accent data-[state=active]:text-white data-[state=active]:shadow-colored hover:bg-white/50 dark:hover:bg-white/10 dark:text-foreground"
            >
              <span className="mr-1">
                {index === 0 ? '🗃️' : index === 1 ? '📈' : index === 2 ? '💰' : '📊'}
              </span>
              {sheet.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {availableSheets.map((sheet) => (
          <TabsContent key={sheet.key} value={sheet.key} className="w-full mt-6 animate-fade-in">
            <div className="w-full rounded-xl border border-border/50 bg-gradient-to-br from-card via-blue-50/10 to-green-50/10 dark:from-card dark:via-blue-950/20 dark:to-green-950/20 shadow-colored dark:shadow-dark-elevated overflow-hidden">
              <DataTable data={data[sheet.key]} sheetName={sheet.name} sheetKey={sheet.key} />
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
